<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Organization extends Model
{
    protected $fillable = [
        'name',
        'slug',
        'description',
        'about',
        'founded_date',
        'logo',
        'cover_image',
        'email',
        'phone',
        'website',
        'social_links',
        'contact_info',
        'status',
        'is_page_mode',
        'allow_public_posts',
        'created_by',
    ];

    protected $casts = [
        'social_links' => 'array',
        'contact_info' => 'array',
        'is_page_mode' => 'boolean',
        'allow_public_posts' => 'boolean',
        'founded_date' => 'date',
    ];

    /**
     * Get the user who created this organization
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get all members of this organization
     */
    public function members(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'organization_members')
            ->withPivot(['role', 'status', 'joined_at'])
            ->withTimestamps();
    }

    /**
     * Get active members only
     */
    public function activeMembers(): BelongsToMany
    {
        return $this->members()->wherePivot('status', 'active');
    }

    /**
     * Get officers of this organization
     */
    public function officers(): BelongsToMany
    {
        return $this->members()->whereIn('organization_members.role', ['officer', 'president']);
    }

    /**
     * Get posts by this organization
     */
    public function posts(): HasMany
    {
        return $this->hasMany(Post::class);
    }

    /**
     * Get followers of this organization
     */
    public function followers(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'organization_followers')
            ->withTimestamps();
    }

    /**
     * Get groups associated with this organization
     */
    public function groups(): HasMany
    {
        return $this->hasMany(Group::class);
    }

    /**
     * Check if user is following this organization
     */
    public function isFollowedBy(User $user): bool
    {
        return $this->followers()->where('user_id', $user->id)->exists();
    }

    /**
     * Check if user can post to this organization
     */
    public function userCanPost(User $user): bool
    {
        // If page mode and public posts allowed, any follower can post
        if ($this->is_page_mode && $this->allow_public_posts && $this->isFollowedBy($user)) {
            return true;
        }

        // Otherwise, only officers and admins can post
        if ($user->isAdmin() || $this->created_by === $user->id) {
            return true;
        }

        $membership = $this->members()->where('user_id', $user->id)->first();
        return $membership && in_array($membership->pivot->role, ['officer', 'president']);
    }

    /**
     * Scope for page mode organizations
     */
    public function scopePageMode($query)
    {
        return $query->where('is_page_mode', true);
    }

    /**
     * Scope for traditional organization mode
     */
    public function scopeOrganizationMode($query)
    {
        return $query->where('is_page_mode', false);
    }
}
