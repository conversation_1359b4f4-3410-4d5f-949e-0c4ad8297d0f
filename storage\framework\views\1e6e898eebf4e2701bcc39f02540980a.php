<?php if (isset($component)) { $__componentOriginal4969f54a92451522b65593c595a4fb0c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4969f54a92451522b65593c595a4fb0c = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.unilink-layout','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('unilink-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <!-- Page Header -->
    <div class="mb-8">
        <div class="flex items-center space-x-4">
            <a href="<?php echo e(route('dashboard')); ?>" class="text-gray-400 hover:text-gray-600">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                </svg>
            </a>
            <div>
                <h1 class="text-2xl font-bold text-gray-900"><?php echo e($post->title); ?></h1>
                <p class="text-gray-600 mt-1">
                    by <?php echo e($post->user->name); ?>

                    <?php if($post->organization): ?>
                        in <?php echo e($post->organization->name); ?>

                    <?php endif; ?>
                    • <?php echo e($post->published_at->format('M j, Y \a\t g:i A')); ?>

                </p>
            </div>
        </div>
    </div>

    <!-- Post Content -->
    <div class="max-w-4xl">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            <!-- Post Header -->
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center space-x-4">
                    <?php if($post->organization): ?>
                        <img class="h-16 w-16 rounded-full"
                             src="<?php echo e($post->organization->logo ? \Illuminate\Support\Facades\Storage::disk('public')->url($post->organization->logo) : 'https://ui-avatars.com/api/?name=' . urlencode($post->organization->name) . '&color=3B82F6&background=DBEAFE'); ?>"
                             alt="<?php echo e($post->organization->name); ?>">
                    <?php else: ?>
                        <img class="h-16 w-16 rounded-full"
                             src="<?php echo e($post->user->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url($post->user->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($post->user->name) . '&color=7BC74D&background=EEEEEE'); ?>"
                             alt="<?php echo e($post->user->name); ?>">
                    <?php endif; ?>
                    <div class="flex-1">
                        <div class="flex items-center space-x-3">
                            <h2 class="text-xl font-semibold text-gray-900">
                                <?php echo e($post->organization ? $post->organization->name : $post->user->name); ?>

                            </h2>
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium 
                                <?php if($post->type === 'event'): ?> bg-blue-100 text-blue-800
                                <?php elseif($post->type === 'announcement'): ?> bg-yellow-100 text-yellow-800
                                <?php elseif($post->type === 'financial_report'): ?> bg-green-100 text-green-800
                                <?php else: ?> bg-gray-100 text-gray-800
                                <?php endif; ?>">
                                <?php echo e(ucfirst(str_replace('_', ' ', $post->type))); ?>

                            </span>
                        </div>
                        <p class="text-gray-600 mt-1">
                            <?php if($post->organization): ?>
                                Posted by <?php echo e($post->user->name); ?> • 
                            <?php endif; ?>
                            <?php echo e($post->published_at->format('M j, Y \a\t g:i A')); ?>

                        </p>
                    </div>
                    
                    <!-- Post Actions -->
                    <?php if(auth()->id() === $post->user_id || auth()->user()->isAdmin()): ?>
                        <div class="flex items-center space-x-2">
                            <a href="<?php echo e(route('posts.edit', $post)); ?>" class="text-gray-400 hover:text-gray-600">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                </svg>
                            </a>
                            <form action="<?php echo e(route('posts.destroy', $post)); ?>" method="POST" class="inline">
                                <?php echo csrf_field(); ?>
                                <?php echo method_field('DELETE'); ?>
                                <button type="submit" onclick="return confirm('Are you sure you want to delete this post?')" class="text-gray-400 hover:text-red-600">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                    </svg>
                                </button>
                            </form>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Post Content -->
            <div class="p-6">
                <h1 class="text-3xl font-bold text-gray-900 mb-4"><?php echo e($post->title); ?></h1>
                <div class="prose max-w-none text-gray-700 mb-6">
                    <?php echo nl2br(e($post->content)); ?>

                </div>

                <!-- Images -->
                <?php if($post->images && count($post->images) > 0): ?>
                    <div class="mb-6">
                        <?php if(count($post->images) === 1): ?>
                            <img src="<?php echo e(\Illuminate\Support\Facades\Storage::disk('public')->url($post->images[0])); ?>"
                                 alt="Post image"
                                 class="w-full max-h-96 object-cover rounded-lg cursor-pointer hover:opacity-95 transition-opacity"
                                 onclick="openImageModal(<?php echo e(json_encode($post->images)); ?>, 0)">
                        <?php else: ?>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <?php $__currentLoopData = $post->images; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <img src="<?php echo e(\Illuminate\Support\Facades\Storage::disk('public')->url($image)); ?>"
                                         alt="Post image"
                                         class="w-full h-64 object-cover rounded-lg cursor-pointer hover:opacity-95 transition-opacity"
                                         onclick="openImageModal(<?php echo e(json_encode($post->images)); ?>, <?php echo e($index); ?>)">
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>

                <!-- Facebook Embed -->
                <?php if($post->facebook_embed_url): ?>
                    <div class="mb-6">
                        <iframe src="<?php echo e($post->facebook_embed_url); ?>" width="100%" height="400" style="border:none;overflow:hidden" scrolling="no" frameborder="0" allowfullscreen="true"></iframe>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Post Actions -->
            <div class="px-6 py-4 bg-gray-50 border-t border-gray-200">
                <div class="flex items-center space-x-6">
                    <button onclick="toggleLike(<?php echo e($post->id); ?>)" 
                            class="flex items-center space-x-2 text-gray-500 hover:text-red-600 transition-colors"
                            id="like-btn-<?php echo e($post->id); ?>">
                        <svg class="w-6 h-6 <?php echo e($post->isLikedBy(auth()->user()) ? 'text-red-600 fill-current' : ''); ?>" 
                             fill="<?php echo e($post->isLikedBy(auth()->user()) ? 'currentColor' : 'none'); ?>" 
                             stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                        </svg>
                        <span id="like-count-<?php echo e($post->id); ?>"><?php echo e($post->likes->count()); ?> likes</span>
                    </button>
                    <div class="flex items-center space-x-2 text-gray-500">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                        </svg>
                        <span><?php echo e($post->comments->count()); ?> comments</span>
                    </div>
                    <button class="flex items-center space-x-2 text-gray-500 hover:text-blue-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                        </svg>
                        <span>Share</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- Comments Section -->
        <div class="mt-8 bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Comments (<?php echo e($post->comments->count()); ?>)</h3>
                
                <!-- Add Comment Form -->
                <?php if(auth()->guard()->check()): ?>
                    <form action="<?php echo e(route('posts.comments.store', $post)); ?>" method="POST" class="mb-6">
                        <?php echo csrf_field(); ?>
                        <div class="flex space-x-3">
                            <img class="h-10 w-10 rounded-full" src="<?php echo e(auth()->user()->avatar ?? 'https://ui-avatars.com/api/?name=' . urlencode(auth()->user()->name) . '&color=7BC74D&background=EEEEEE'); ?>" alt="<?php echo e(auth()->user()->name); ?>">
                            <div class="flex-1">
                                <textarea name="content" rows="3" placeholder="Write a comment..." class="w-full border-gray-300 rounded-md shadow-sm focus:ring-custom-green focus:border-custom-green resize-none" required></textarea>
                                <div class="mt-2 flex justify-end">
                                    <button type="submit" class="px-4 py-2 bg-custom-green text-white text-sm font-medium rounded-md hover:bg-custom-second-darkest focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-custom-green">
                                        Post Comment
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                <?php endif; ?>

                <!-- Comments List -->
                <div class="space-y-4">
                    <?php $__empty_1 = true; $__currentLoopData = $post->comments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $comment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <div class="flex space-x-3">
                            <img class="h-10 w-10 rounded-full" src="<?php echo e($comment->user->avatar ?? 'https://ui-avatars.com/api/?name=' . urlencode($comment->user->name) . '&color=7BC74D&background=EEEEEE'); ?>" alt="<?php echo e($comment->user->name); ?>">
                            <div class="flex-1">
                                <div class="bg-gray-50 rounded-lg p-3">
                                    <div class="flex items-center space-x-2 mb-1">
                                        <span class="font-medium text-gray-900"><?php echo e($comment->user->name); ?></span>
                                        <span class="text-sm text-gray-500"><?php echo e($comment->created_at->diffForHumans()); ?></span>
                                    </div>
                                    <p class="text-gray-700"><?php echo nl2br(e($comment->content)); ?></p>
                                </div>
                                
                                <!-- Comment Actions -->
                                <div class="flex items-center space-x-4 mt-2 text-sm">
                                    <button class="text-gray-500 hover:text-blue-600">Like</button>
                                    <button class="text-gray-500 hover:text-blue-600">Reply</button>
                                    <?php if(auth()->id() === $comment->user_id || auth()->user()->isAdmin()): ?>
                                        <button class="text-gray-500 hover:text-red-600">Delete</button>
                                    <?php endif; ?>
                                </div>

                                <!-- Replies -->
                                <?php if($comment->replies->count() > 0): ?>
                                    <div class="mt-4 ml-4 space-y-3">
                                        <?php $__currentLoopData = $comment->replies; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $reply): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <div class="flex space-x-3">
                                                <img class="h-8 w-8 rounded-full" src="<?php echo e($reply->user->avatar ?? 'https://ui-avatars.com/api/?name=' . urlencode($reply->user->name) . '&color=7BC74D&background=EEEEEE'); ?>" alt="<?php echo e($reply->user->name); ?>">
                                                <div class="flex-1">
                                                    <div class="bg-gray-50 rounded-lg p-3">
                                                        <div class="flex items-center space-x-2 mb-1">
                                                            <span class="font-medium text-gray-900"><?php echo e($reply->user->name); ?></span>
                                                            <span class="text-sm text-gray-500"><?php echo e($reply->created_at->diffForHumans()); ?></span>
                                                        </div>
                                                        <p class="text-gray-700"><?php echo nl2br(e($reply->content)); ?></p>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <p class="text-gray-500 text-center py-4">No comments yet. Be the first to comment!</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <script>
    async function toggleLike(postId) {
        try {
            const response = await fetch(`/posts/${postId}/like`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            const data = await response.json();
            
            if (data.success) {
                const likeBtn = document.getElementById(`like-btn-${postId}`);
                const likeCount = document.getElementById(`like-count-${postId}`);
                const heartIcon = likeBtn.querySelector('svg');
                
                if (data.liked) {
                    heartIcon.classList.add('text-red-600', 'fill-current');
                    heartIcon.setAttribute('fill', 'currentColor');
                } else {
                    heartIcon.classList.remove('text-red-600', 'fill-current');
                    heartIcon.setAttribute('fill', 'none');
                }
                
                likeCount.textContent = `${data.likes_count} likes`;
            }
        } catch (error) {
            console.error('Error toggling like:', error);
        }
    }
    </script>

    <!-- Image Modal -->
    <?php echo $__env->make('components.image-modal', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4969f54a92451522b65593c595a4fb0c)): ?>
<?php $attributes = $__attributesOriginal4969f54a92451522b65593c595a4fb0c; ?>
<?php unset($__attributesOriginal4969f54a92451522b65593c595a4fb0c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4969f54a92451522b65593c595a4fb0c)): ?>
<?php $component = $__componentOriginal4969f54a92451522b65593c595a4fb0c; ?>
<?php unset($__componentOriginal4969f54a92451522b65593c595a4fb0c); ?>
<?php endif; ?>
<?php /**PATH C:\xampp\htdocs\LARAVEL_PROJECTS\lara_unilink\resources\views/posts/show.blade.php ENDPATH**/ ?>