<?php if (isset($component)) { $__componentOriginal4969f54a92451522b65593c595a4fb0c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4969f54a92451522b65593c595a4fb0c = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.unilink-layout','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('unilink-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <!-- Organization Page Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden mb-6">
        <!-- Cover Image -->
        <div class="h-48 bg-gradient-to-r from-blue-500 to-purple-600 relative">
            <?php if($organization->cover_image): ?>
                <img src="<?php echo e(Storage::disk('public')->url($organization->cover_image)); ?>" alt="<?php echo e($organization->name); ?>" class="w-full h-full object-cover">
            <?php endif; ?>
            
            <!-- Action Buttons -->
            <div class="absolute top-4 right-4 flex space-x-2">
                <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('organization-follower', ['organization' => $organization]);

$__html = app('livewire')->mount($__name, $__params, 'lw-4130964988-0', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>

                <?php if($organization->userCanPost(auth()->user())): ?>
                    <button onclick="openCreatePostModal()" class="bg-green-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-green-700">
                        Create Post
                    </button>
                <?php endif; ?>

                <?php if($userMembership && in_array($userMembership->pivot->role, ['officer', 'president']) || auth()->user()->isAdmin()): ?>
                    <a href="<?php echo e(route('organizations.edit', $organization)); ?>" class="bg-gray-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-gray-700">
                        Manage
                    </a>
                <?php endif; ?>
            </div>
        </div>

        <!-- Organization Info -->
        <div class="p-6">
            <div class="flex items-start space-x-4">
                <!-- Logo -->
                <div class="flex-shrink-0">
                    <div class="w-20 h-20 bg-white rounded-lg shadow-md flex items-center justify-center -mt-10 border-4 border-white">
                        <?php if($organization->logo): ?>
                            <img src="<?php echo e(Storage::disk('public')->url($organization->logo)); ?>" alt="<?php echo e($organization->name); ?>" class="w-16 h-16 rounded-lg object-cover">
                        <?php else: ?>
                            <div class="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center">
                                <span class="text-blue-600 font-bold text-lg"><?php echo e(substr($organization->name, 0, 2)); ?></span>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Organization Details -->
                <div class="flex-1 min-w-0">
                    <div class="flex items-center justify-between">
                        <h1 class="text-2xl font-bold text-gray-900"><?php echo e($organization->name); ?></h1>
                        <div class="flex items-center space-x-2">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                Official Page
                            </span>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo e($organization->status === 'active' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'); ?>">
                                <?php echo e(ucfirst($organization->status)); ?>

                            </span>
                        </div>
                    </div>
                    
                    <p class="text-gray-600 mt-2"><?php echo e($organization->description); ?></p>
                    
                    <?php if($organization->about): ?>
                        <div class="mt-3">
                            <h3 class="text-sm font-medium text-gray-900">About</h3>
                            <p class="text-gray-600 text-sm mt-1"><?php echo e($organization->about); ?></p>
                        </div>
                    <?php endif; ?>
                    
                    <!-- Stats -->
                    <div class="flex items-center space-x-6 mt-4 text-sm text-gray-500">
                        <div class="flex items-center">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                            </svg>
                            <?php echo e($followersCount); ?> followers
                        </div>
                        <div class="flex items-center">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                            </svg>
                            <?php echo e($organization->officers->count()); ?> officers
                        </div>
                        <?php if($organization->founded_date): ?>
                            <div class="flex items-center">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a1 1 0 011-1h6a1 1 0 011 1v4h3a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9a2 2 0 012-2h3z" />
                                </svg>
                                Founded <?php echo e($organization->founded_date->format('M Y')); ?>

                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Contact Info -->
                    <?php if($organization->email || $organization->phone || $organization->website): ?>
                        <div class="flex items-center space-x-4 mt-4">
                            <?php if($organization->email): ?>
                                <a href="mailto:<?php echo e($organization->email); ?>" class="text-blue-600 hover:text-blue-800 text-sm flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                    </svg>
                                    <?php echo e($organization->email); ?>

                                </a>
                            <?php endif; ?>
                            <?php if($organization->phone): ?>
                                <a href="tel:<?php echo e($organization->phone); ?>" class="text-blue-600 hover:text-blue-800 text-sm flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                                    </svg>
                                    <?php echo e($organization->phone); ?>

                                </a>
                            <?php endif; ?>
                            <?php if($organization->website): ?>
                                <a href="<?php echo e($organization->website); ?>" target="_blank" class="text-blue-600 hover:text-blue-800 text-sm flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                                    </svg>
                                    Website
                                </a>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Content -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Announcements & Posts -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="p-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900">Latest Announcements</h2>
                </div>
                
                <?php if($organization->posts->count() > 0): ?>
                    <div class="divide-y divide-gray-200">
                        <?php $__currentLoopData = $organization->posts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $post): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="p-4">
                                <div class="flex items-start space-x-3">
                                    <img class="h-10 w-10 rounded-full" src="<?php echo e($post->user->avatar ?? 'https://ui-avatars.com/api/?name=' . urlencode($post->user->name) . '&color=7F9CF5&background=EBF4FF'); ?>" alt="<?php echo e($post->user->name); ?>">
                                    <div class="flex-1 min-w-0">
                                        <div class="flex items-center space-x-2">
                                            <p class="text-sm font-medium text-gray-900"><?php echo e($organization->name); ?></p>
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                Official
                                            </span>
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                <?php echo e(ucfirst($post->type)); ?>

                                            </span>
                                            <p class="text-sm text-gray-500"><?php echo e($post->published_at->diffForHumans()); ?></p>
                                        </div>
                                        <h3 class="text-lg font-semibold text-gray-900 mt-1"><?php echo e($post->title); ?></h3>
                                        <p class="text-gray-700 mt-2"><?php echo e(Str::limit($post->content, 300)); ?></p>
                                        
                                        <?php if($post->images && count($post->images) > 0): ?>
                                            <div class="mt-3 grid grid-cols-2 gap-2">
                                                <?php $__currentLoopData = array_slice($post->images, 0, 4); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <img src="<?php echo e(Storage::disk('public')->url($image)); ?>" alt="Post image" class="rounded-lg object-cover h-32 w-full">
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <div class="flex items-center space-x-4 mt-3 text-sm text-gray-500">
                                            <button class="flex items-center space-x-1 hover:text-blue-600">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                                                </svg>
                                                <span><?php echo e($post->likes->count()); ?></span>
                                            </button>
                                            <button class="flex items-center space-x-1 hover:text-blue-600">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                                                </svg>
                                                <span><?php echo e($post->comments->count()); ?></span>
                                            </button>
                                            <button class="flex items-center space-x-1 hover:text-blue-600">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                                                </svg>
                                                <span>Share</span>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                <?php else: ?>
                    <div class="p-8 text-center">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-gray-900">No announcements yet</h3>
                        <p class="mt-1 text-sm text-gray-500">This organization hasn't posted any announcements yet.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Officers -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="p-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900">Officers</h3>
                </div>
                
                <div class="p-4">
                    <div class="space-y-3">
                        <?php $__currentLoopData = $organization->officers->take(5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $officer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="flex items-center space-x-3">
                                <img class="h-8 w-8 rounded-full" src="<?php echo e($officer->avatar ? Storage::disk('public')->url($officer->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($officer->name) . '&color=7F9CF5&background=EBF4FF'); ?>" alt="<?php echo e($officer->name); ?>">
                                <div class="flex-1 min-w-0">
                                    <p class="text-sm font-medium text-gray-900 truncate"><?php echo e($officer->name); ?></p>
                                    <p class="text-xs text-gray-500"><?php echo e(ucfirst($officer->pivot->role)); ?></p>
                                </div>
                                <?php if($officer->pivot->role === 'president'): ?>
                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800">
                                        President
                                    </span>
                                <?php elseif($officer->pivot->role === 'officer'): ?>
                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                        Officer
                                    </span>
                                <?php endif; ?>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            </div>

            <!-- Related Groups -->
            <?php if($organization->groups->count() > 0): ?>
                <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                    <div class="p-4 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900">Related Groups</h3>
                    </div>
                    
                    <div class="p-4">
                        <div class="space-y-3">
                            <?php $__currentLoopData = $organization->groups->take(5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $group): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                                        <?php if($group->logo): ?>
                                            <img src="<?php echo e(Storage::disk('public')->url($group->logo)); ?>" alt="<?php echo e($group->name); ?>" class="w-6 h-6 rounded object-cover">
                                        <?php else: ?>
                                            <span class="text-blue-600 font-bold text-xs"><?php echo e(substr($group->name, 0, 2)); ?></span>
                                        <?php endif; ?>
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <a href="<?php echo e(route('groups.show', $group)); ?>" class="text-sm font-medium text-gray-900 hover:text-blue-600 truncate block">
                                            <?php echo e($group->name); ?>

                                        </a>
                                        <p class="text-xs text-gray-500"><?php echo e($group->activeMembers->count()); ?> members</p>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Post Creation Modal -->
    <?php if($organization->userCanPost(auth()->user())): ?>
        <?php if (isset($component)) { $__componentOriginal53ce3cc51bf3d8436ea55e8f43727e33 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal53ce3cc51bf3d8436ea55e8f43727e33 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.post-creation-modal','data' => ['organization' => $organization]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('post-creation-modal'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['organization' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($organization)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal53ce3cc51bf3d8436ea55e8f43727e33)): ?>
<?php $attributes = $__attributesOriginal53ce3cc51bf3d8436ea55e8f43727e33; ?>
<?php unset($__attributesOriginal53ce3cc51bf3d8436ea55e8f43727e33); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal53ce3cc51bf3d8436ea55e8f43727e33)): ?>
<?php $component = $__componentOriginal53ce3cc51bf3d8436ea55e8f43727e33; ?>
<?php unset($__componentOriginal53ce3cc51bf3d8436ea55e8f43727e33); ?>
<?php endif; ?>
    <?php endif; ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4969f54a92451522b65593c595a4fb0c)): ?>
<?php $attributes = $__attributesOriginal4969f54a92451522b65593c595a4fb0c; ?>
<?php unset($__attributesOriginal4969f54a92451522b65593c595a4fb0c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4969f54a92451522b65593c595a4fb0c)): ?>
<?php $component = $__componentOriginal4969f54a92451522b65593c595a4fb0c; ?>
<?php unset($__componentOriginal4969f54a92451522b65593c595a4fb0c); ?>
<?php endif; ?>
<?php /**PATH C:\xampp\htdocs\LARAVEL_PROJECTS\lara_unilink\resources\views/organizations/page.blade.php ENDPATH**/ ?>